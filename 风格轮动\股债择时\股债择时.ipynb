import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import seaborn as sns
from pyrb import ConstrainedRiskBudgeting
# 设置绘图风格
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加当前目录到Python路径
sys.path.append('D:\财通金工\数据库')
from database import DatabaseManager
config = {
  'host': '*************',
  'port': 3306,
  'user': 'ct_wind_user',
  'password': 'Ctjg2025',
  'database': 'windsh'
}
db = DatabaseManager(config)

def calculate_profolio_annual_performance(backtest_results, freq='M'):
    """
    计算年度业绩指标统计

    Args:
        backtest_results: 回测结果DataFrame

    Returns:
        pandas.DataFrame: 年度业绩统计表
    """
    if backtest_results.empty or 'strategy_nav' not in backtest_results.columns:
        logging.info("警告：回测结果数据为空或缺少必要列")
        return pd.DataFrame()

    # 确保日期列为datetime类型
    if 'date' in backtest_results.columns:
        backtest_results['date'] = pd.to_datetime(backtest_results['date'])
        backtest_results = backtest_results.set_index('date')

    # 计算基准净值
    # benchmark_nav = backtest_results['benchmark_nav']
    # benchmark_returns = backtest_results['benchmark_return']

    # 按年分组计算业绩
    annual_stats = []

    # 获取所有年份
    years = sorted(backtest_results.index.year.unique())

    if freq == 'M':
        count_per_year = 12
    elif freq == 'D':
        count_per_year = 252
    elif freq == 'W':
        count_per_year = 52
    else:
        raise ValueError("不支持的频率，请选择'M', 'D'或'W'")

    for year in years:
        year_data = backtest_results[backtest_results.index.year == year]

        if len(year_data) < 2:
            continue

        # 策略年度收益率
        start_nav = year_data['strategy_nav'].iloc[0]
        end_nav = year_data['strategy_nav'].iloc[-1]
        strategy_return = (end_nav / start_nav - 1) * 100

        # 基准年度收益率
        start_benchmark = year_data['benchmark_nav'].iloc[0]
        end_benchmark = year_data['benchmark_nav'].iloc[-1]
        benchmark_return = (end_benchmark / start_benchmark - 1) * 100

        # 超额收益
        excess_return = strategy_return - benchmark_return if not np.isnan(benchmark_return) else np.nan

        # 年度波动率
        year_returns = year_data['strategy_return'].dropna()
        volatility = year_returns.std() * np.sqrt(count_per_year) * 100 if len(year_returns) > 1 else np.nan

        # 年度最大回撤
        year_nav = year_data['strategy_nav']
        cumulative_max = year_nav.cummax()
        drawdown = (year_nav - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min() * 100

        # 年度夏普比率
        risk_free_rate = 2.0  # 假设无风险利率2%
        sharpe_ratio = (strategy_return - risk_free_rate) / (volatility) if not np.isnan(volatility) and volatility > 0 else np.nan

        # 当年月度胜率
        # monthly_return = year_returns.resample('ME').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        # monthly_benchmark_return = year_data['benchmark_return'].resample('ME').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        monthly_excess_return = year_returns - year_data['benchmark_return']
        win_rate = (monthly_excess_return > 0).mean() * 100 if len(monthly_excess_return) > 0 else np.nan

        # 当年月度赔率
        if len(monthly_excess_return) > 0 and len(monthly_excess_return[monthly_excess_return < 0]) > 0:
            loss_rate = - monthly_excess_return[monthly_excess_return >= 0].mean() / monthly_excess_return[monthly_excess_return < 0].mean()
        else:
            loss_rate = np.nan

        annual_stats.append({
            '年份': year,
            '组合收益(%)': round(strategy_return, 2),
            '基准收益(%)': round(benchmark_return, 2) if not np.isnan(benchmark_return) else '--',
            '超额收益(%)': round(excess_return, 2) if not np.isnan(excess_return) else '--',
            '年化波动率(%)': round(volatility, 2) if not np.isnan(volatility) else '--',
            '最大回撤(%)': round(max_drawdown, 2),
            '夏普比率': round(sharpe_ratio, 2) if not np.isnan(sharpe_ratio) else '--',
            '胜率(%)': round(win_rate, 2) if not np.isnan(win_rate) else '--',
            '赔率': round(loss_rate, 2) if not np.isnan(loss_rate) else '--'
        })

    # 创建DataFrame
    annual_df = pd.DataFrame(annual_stats)

    # 计算全样本统计指标（年化口径）
    if len(backtest_results) >= 2:
        # 计算总的交易天数和年数
        trading_days = len(backtest_results)
        years_total = trading_days / count_per_year

        # 全样本年化收益率（策略）
        total_nav_ratio = backtest_results['strategy_nav'].iloc[-1] / backtest_results['strategy_nav'].iloc[0]
        annualized_strategy_return = ((total_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

        # 全样本年化收益率（基准）
        benchmark_nav_ratio = backtest_results['benchmark_nav'].iloc[-1] / backtest_results['benchmark_nav'].iloc[0]
        annualized_benchmark_return = ((benchmark_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

        # 全样本年化超额收益
        annualized_excess_return = annualized_strategy_return - annualized_benchmark_return if not np.isnan(annualized_benchmark_return) else np.nan

        # 全样本年化波动率
        all_returns = backtest_results['strategy_return'].dropna()
        total_volatility = all_returns.std() * np.sqrt(count_per_year) * 100 if len(all_returns) > 1 else np.nan

        # 全样本最大回撤
        cumulative_max = backtest_results['strategy_nav'].cummax()
        drawdown = (backtest_results['strategy_nav'] - cumulative_max) / cumulative_max
        total_max_drawdown = drawdown.min() * 100

        # 全样本夏普比率
        total_sharpe_ratio = (annualized_strategy_return - 2.0) / total_volatility if not np.isnan(total_volatility) and total_volatility > 0 else np.nan

        # 全样本胜率
        # monthly_return = all_returns.resample('M').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        # monthly_benchmark_return = benchmark_returns.resample('M').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        monthly_excess_return = all_returns - backtest_results['benchmark_return']
        total_win_rate = (monthly_excess_return > 0).mean() * 100 if len(monthly_excess_return) > 0 else np.nan

        # 赔率
        if len(monthly_excess_return) > 0 and len(monthly_excess_return[monthly_excess_return < 0]) > 0:
            total_loss_rate = - monthly_excess_return[monthly_excess_return >= 0].mean() / monthly_excess_return[monthly_excess_return < 0].mean()
        else:
            total_loss_rate = np.nan
        
        # 添加全样本统计行（年化口径）
        total_stats = {
            '年份': '全样本(年化)',
            '组合收益(%)': round(annualized_strategy_return, 2),
            '基准收益(%)': round(annualized_benchmark_return, 2) if not np.isnan(annualized_benchmark_return) else '--',
            '超额收益(%)': round(annualized_excess_return, 2) if not np.isnan(annualized_excess_return) else '--',
            '年化波动率(%)': round(total_volatility, 2) if not np.isnan(total_volatility) else '--',
            '最大回撤(%)': round(total_max_drawdown, 2),
            '夏普比率': round(total_sharpe_ratio, 2) if not np.isnan(total_sharpe_ratio) else '--',
            '胜率(%)': round(total_win_rate, 2) if not np.isnan(total_win_rate) else '--',
            '赔率': round(total_loss_rate, 2) if not np.isnan(total_loss_rate) else '--'
        }

        # 使用pd.concat添加全样本统计行
        total_df = pd.DataFrame([total_stats])
        annual_df = pd.concat([annual_df, total_df], ignore_index=True)

    # 打印结果
    logging.info("\n" + "="*80)
    logging.info("年度业绩指标统计")
    logging.info("="*80)
    logging.info(annual_df.to_string(index=False))

    return annual_df

def calculate_annual_performance(backtest_results):
    """
    计算年度业绩指标统计

    Args:
        backtest_results: 回测结果DataFrame

    Returns:
        pandas.DataFrame: 年度业绩统计表
    """
    if backtest_results.empty or 'strategy_nav' not in backtest_results.columns:
        logging.info("警告：回测结果数据为空或缺少必要列")
        return pd.DataFrame()

    # 确保日期列为datetime类型
    if 'date' in backtest_results.columns:
        backtest_results['date'] = pd.to_datetime(backtest_results['date'])
        backtest_results = backtest_results.set_index('date')

    # 计算基准净值
    # benchmark_nav = backtest_results['benchmark_nav']
    # benchmark_returns = backtest_results['benchmark_return']

    # 按年分组计算业绩
    annual_stats = []

    # 获取所有年份
    years = sorted(backtest_results.index.year.unique())

    for year in years:
        year_data = backtest_results[backtest_results.index.year == year]

        if len(year_data) < 2:
            continue

        # 策略年度收益率
        start_nav = year_data['strategy_nav'].iloc[0]
        end_nav = year_data['strategy_nav'].iloc[-1]
        strategy_return = (end_nav / start_nav - 1) * 100

        # 基准年度收益率
        start_benchmark = year_data['benchmark_nav'].iloc[0]
        end_benchmark = year_data['benchmark_nav'].iloc[-1]
        benchmark_return = (end_benchmark / start_benchmark - 1) * 100

        # 超额收益
        excess_return = strategy_return - benchmark_return if not np.isnan(benchmark_return) else np.nan

        # 年度波动率
        year_returns = year_data['strategy_return'].dropna()
        volatility = year_returns.std() * np.sqrt(252) * 100 if len(year_returns) > 1 else np.nan

        # 年度最大回撤
        year_nav = year_data['strategy_nav']
        cumulative_max = year_nav.cummax()
        drawdown = (year_nav - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min() * 100

        # 年度夏普比率
        risk_free_rate = 2.0  # 假设无风险利率2%
        sharpe_ratio = (strategy_return - risk_free_rate) / (volatility) if not np.isnan(volatility) and volatility > 0 else np.nan

        # 当年月度胜率
        # monthly_return = year_returns.resample('ME').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        # monthly_benchmark_return = year_data['benchmark_return'].resample('ME').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        monthly_excess_return = year_returns - year_data['benchmark_return']
        tp = year_returns[(year_returns == year_data['benchmark_return']) & (year_data['benchmark_return'] > 0)]
        tn = year_returns[(year_returns != year_data['benchmark_return']) & (year_data['benchmark_return'] < 0)]
        fn = year_returns[(year_returns != year_data['benchmark_return']) & (year_data['benchmark_return'] > 0)]
        fp = year_returns[(year_returns == year_data['benchmark_return']) & (year_data['benchmark_return'] < 0)]
        win_rate = (tp.count() + tn.count()) / (year_returns.count()) * 100 if year_returns.count() > 0 else np.nan

        # 当年月度赔率
        if len(year_returns) > 0 and len(year_returns[year_returns < 0]) > 0:
            loss_rate = monthly_excess_return[((year_returns == year_data['benchmark_return']) & (year_data['benchmark_return'] > 0)) |
                                               ((year_returns != year_data['benchmark_return']) & (year_data['benchmark_return'] < 0))].abs().mean() / \
                      monthly_excess_return[((year_returns != year_data['benchmark_return']) & (year_data['benchmark_return'] > 0)) |
                                           ((year_returns == year_data['benchmark_return']) & (year_data['benchmark_return'] < 0))].abs().mean()
        else:
            loss_rate = np.nan

        precision = tp.count() / (tp.count() + fp.count()) if tp.count() + fp.count() > 0 else np.nan
        recall = tp.count() / (tp.count() + fn.count()) if tp.count() + fn.count() > 0 else np.nan
        specificity = tn.count() / (tn.count() + fp.count()) if tn.count() + fp.count() > 0 else np.nan

        annual_stats.append({
            '年份': year,
            '组合收益(%)': round(strategy_return, 2),
            '基准收益(%)': round(benchmark_return, 2) if not np.isnan(benchmark_return) else '--',
            '超额收益(%)': round(excess_return, 2) if not np.isnan(excess_return) else '--',
            '年化波动率(%)': round(volatility, 2) if not np.isnan(volatility) else '--',
            '最大回撤(%)': round(max_drawdown, 2),
            '夏普比率': round(sharpe_ratio, 2) if not np.isnan(sharpe_ratio) else '--',
            '胜率(%)': round(win_rate, 2) if not np.isnan(win_rate) else '--',
            '赔率': round(loss_rate, 2) if not np.isnan(loss_rate) else '--',
            '精确率': round(precision, 2) if not np.isnan(precision) else '--',
            '召回率': round(recall, 2) if not np.isnan(recall) else '--',
            '特异度': round(specificity, 2) if not np.isnan(specificity) else '--'
        })

    # 创建DataFrame
    annual_df = pd.DataFrame(annual_stats)

    # 计算全样本统计指标（年化口径）
    if len(backtest_results) >= 2:
        # 计算总的交易天数和年数
        trading_days = len(backtest_results)
        years_total = trading_days / 12

        # 全样本年化收益率（策略）
        total_nav_ratio = backtest_results['strategy_nav'].iloc[-1] / backtest_results['strategy_nav'].iloc[0]
        annualized_strategy_return = ((total_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

        # 全样本年化收益率（基准）
        benchmark_nav_ratio = backtest_results['benchmark_nav'].iloc[-1] / backtest_results['benchmark_nav'].iloc[0]
        annualized_benchmark_return = ((benchmark_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

        # 全样本年化超额收益
        annualized_excess_return = annualized_strategy_return - annualized_benchmark_return if not np.isnan(annualized_benchmark_return) else np.nan

        # 全样本年化波动率
        all_returns = backtest_results['strategy_return'].dropna()
        total_volatility = all_returns.std() * np.sqrt(12) * 100 if len(all_returns) > 1 else np.nan

        # 全样本最大回撤
        cumulative_max = backtest_results['strategy_nav'].cummax()
        drawdown = (backtest_results['strategy_nav'] - cumulative_max) / cumulative_max
        total_max_drawdown = drawdown.min() * 100

        # 全样本夏普比率
        total_sharpe_ratio = (annualized_strategy_return - 2.0) / total_volatility if not np.isnan(total_volatility) and total_volatility > 0 else np.nan

        # 全样本胜率
        # monthly_return = all_returns.resample('M').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        # monthly_benchmark_return = benchmark_returns.resample('M').apply(
        #     lambda x: (x + 1).prod() - 1
        # )
        monthly_excess_return = all_returns - backtest_results['benchmark_return']
        tp = all_returns[(all_returns == backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] > 0)]
        tn = all_returns[(all_returns != backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] < 0)]
        fn = all_returns[(all_returns != backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] > 0)]
        fp = all_returns[(all_returns == backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] < 0)]
        total_win_rate = (tp.count() + tn.count()) / (all_returns.count()) * 100 if all_returns.count() > 0 else np.nan

        # 赔率
        if len(all_returns) > 0 and len(all_returns[all_returns < 0]) > 0:
            total_loss_rate = monthly_excess_return[((all_returns == backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] > 0)) |
                                           ((all_returns != backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] < 0))].abs().mean() / \
                            monthly_excess_return[((all_returns != backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] > 0)) |
                            ((all_returns == backtest_results['benchmark_return']) & (backtest_results['benchmark_return'] < 0))].abs().mean()
        else:
            total_loss_rate = np.nan

        precision = tp.count() / (tp.count() + fp.count()) if tp.count() + fp.count() > 0 else np.nan
        recall = tp.count() / (tp.count() + fn.count()) if tp.count() + fn.count() > 0 else np.nan
        specificity = tn.count() / (tn.count() + fp.count()) if tn.count() + fp.count() > 0 else np.nan
        
        # 添加全样本统计行（年化口径）
        total_stats = {
            '年份': '全样本(年化)',
            '组合收益(%)': round(annualized_strategy_return, 2),
            '基准收益(%)': round(annualized_benchmark_return, 2) if not np.isnan(annualized_benchmark_return) else '--',
            '超额收益(%)': round(annualized_excess_return, 2) if not np.isnan(annualized_excess_return) else '--',
            '年化波动率(%)': round(total_volatility, 2) if not np.isnan(total_volatility) else '--',
            '最大回撤(%)': round(total_max_drawdown, 2),
            '夏普比率': round(total_sharpe_ratio, 2) if not np.isnan(total_sharpe_ratio) else '--',
            '胜率(%)': round(total_win_rate, 2) if not np.isnan(total_win_rate) else '--',
            '赔率': round(total_loss_rate, 2) if not np.isnan(total_loss_rate) else '--',
            '精确率': round(precision, 2) if not np.isnan(precision) else '--',
            '召回率': round(recall, 2) if not np.isnan(recall) else '--',
            '特异度': round(specificity, 2) if not np.isnan(specificity) else '--'
        }

        # 使用pd.concat添加全样本统计行
        total_df = pd.DataFrame([total_stats])
        annual_df = pd.concat([annual_df, total_df], ignore_index=True)

    # 打印结果
    logging.info("\n" + "="*80)
    logging.info("年度业绩指标统计")
    logging.info("="*80)
    logging.info(annual_df.to_string(index=False))

    return annual_df

def get_rebalance_dates(db, start_date, end_date, frequency='monthly'):
    """
    获取调仓日期

    Args:
        start_date: 开始日期
        end_date: 结束日期
        frequency: 调仓频率，'monthly'或'weekly'

    Returns:
        pandas.DatetimeIndex: 调仓日期列表
    """
    # 获取交易日历
    trading_dates = db.get_trading_date_range(start_date, end_date)

    if trading_dates.empty:
        return pd.DatetimeIndex([])

    rebalance_dates = [pd.to_datetime(start_date)]

    if frequency == 'monthly':
        # 每月最后一个交易日
        for date in trading_dates:
            # 检查是否是当月最后一个交易日
            month_end = date + pd.offsets.MonthEnd(0)
            month_trading_dates = trading_dates[
                (trading_dates >= date.replace(day=1)) &
                (trading_dates <= month_end)
            ]
            if len(month_trading_dates) > 0 and date == month_trading_dates.max():
                rebalance_dates.append(date)

    elif frequency == 'weekly':
        # 每周最后一个交易日
        for date in trading_dates:
            # 检查是否是当周最后一个交易日
            week_end = date + pd.Timedelta(days=(6 - date.weekday()))
            week_trading_dates = trading_dates[
                (trading_dates >= date) &
                (trading_dates <= week_end)
            ]
            if len(week_trading_dates) > 0 and date == week_trading_dates.max():
                rebalance_dates.append(date)

    return pd.DatetimeIndex(rebalance_dates)

def generate_monthly_signals(db, factor_data):
    """
    生成月度交易信号

    Args:
        factor_data: 因子数据DataFrame
        signal_threshold: 信号阈值

    Returns:
        pandas.DataFrame: 包含交易信号的数据
    """
    if factor_data.empty:
        return pd.DataFrame()

    # 获取调仓日期
    start_date = factor_data['date'].min().strftime('%Y%m%d')
    end_date = factor_data['date'].max().strftime('%Y%m%d')
    rebalance_dates = get_rebalance_dates(db, start_date, end_date, 'monthly')

    # 筛选调仓日的因子数据
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    signal_data = factor_data[factor_data['date'].isin(rebalance_dates)].copy()

    # if signal_data.empty:
    #     return pd.DataFrame()

    # # 生成信号（基于综合得分）
    # if 'comprehensive_score' in signal_data.columns:
    #     score_col = 'comprehensive_score'
    # elif 'total_score' in signal_data.columns:
    #     score_col = 'total_score'
    # else:
    #     # 如果没有综合得分，使用第一个得分列
    #     score_cols = [col for col in signal_data.columns if col.endswith('_score')]
    #     if score_cols:
    #         score_col = score_cols[0]
    #     else:
    #         return pd.DataFrame()

    # # 生成权重信号
    # signal_data['equity_weight'] = np.where(
    #     signal_data[score_col] >= signal_threshold, 0.8, 0.2  # 看多时80%股票，看空时20%股票
    # )
    # signal_data['money_fund_weight'] = 1 - signal_data['equity_weight']

    return signal_data.reset_index(drop=True)


def plot_nav_comparison(backtest_results: pd.DataFrame):
    """
    绘制净值曲线对比图
    
    Args:
        backtest_results: 回测结果数据
        title: 图表标题
        
    Returns:
        matplotlib.figure.Figure: 图表对象
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制净值曲线
    ax.plot(backtest_results['date'], backtest_results['strategy_nav'], 
            label='策略', linewidth=2, color='red')
    ax.plot(backtest_results['date'], backtest_results['benchmark_nav'], 
            label='基准', linewidth=2, color='blue')
    ax.plot(backtest_results['date'], backtest_results['strategy_nav']/backtest_results['benchmark_nav'],
            label='策略相对基准', linewidth=2, color='grey')
    
    # 设置图表属性
#     ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('日期', fontsize=12)
    ax.set_ylabel('净值', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    return fig

price_data = pd.read_excel('index_price.xlsx')
price_data = price_data.sort_values('date')
price_data = price_data[(price_data['date']<='20250831')].reset_index(drop=True)
price_data['month'] = price_data['date'].dt.to_period('M')
monthly_price_data = price_data.groupby('month').agg({
    'date': 'last',
    'stock': lambda x: (1 + x/100).prod() - 1,
    'bond': lambda x: (1 + x/100).prod() - 1,
    'convertingbonds': lambda x: (1 + x/100).prod() - 1,
    'sp500ETF': lambda x: (1 + x/100).prod() - 1,
    'aaadebt': lambda x: (1 + x/100).prod() - 1,
    'monetary': lambda x: (1 + x/100).prod() - 1
}).reset_index()
# monthly_price_data['pct_change'] = monthly_price_data['close'].pct_change()
monthly_price_data

dr007_data = db.get_daily_macro_data('20160101','20250831')
# 计算移动平均
dr007_data['dr007_ma1'] = dr007_data['dr007'].rolling(
    window=20, min_periods=1
).mean()
dr007_data['dr007_ma2'] = dr007_data['dr007'].rolling(
    window=60, min_periods=1
).mean()

# 生成信号：下降为宽松(1)，上升为紧缩(0)
dr007_data['dr007_signal'] = np.where(
    dr007_data['dr007_ma1'] < dr007_data['dr007_ma2'], 1, -1
)
dr007_signal = dr007_data[['date', 'dr007', 'dr007_ma1', 'dr007_ma2', 'dr007_signal']].reset_index(drop=True)
dr007_signal = generate_monthly_signals(db, dr007_signal)

# 获取SHIBOR数据
shibor_data = db.get_daily_macro_data('20160101','20250831')
# 计算移动平均
shibor_data['shibor_ma1'] = shibor_data['shibor'].rolling(
    window=20, min_periods=1
).mean()
shibor_data['shibor_ma2'] = shibor_data['shibor'].rolling(
    window=60, min_periods=1
).mean()

# 生成信号：下降为宽松(1)，上升为紧缩(0)
shibor_data['shibor_signal'] = np.where(
    shibor_data['shibor_ma1'] < shibor_data['shibor_ma2'], 1, -1
)
shibor_signal = shibor_data[['date', 'shibor', 'shibor_ma1', 'shibor_ma2', 'shibor_signal']].reset_index(drop=True)
shibor_signal = generate_monthly_signals(db, shibor_signal)

# 获取中长期贷款数据
credit_data = db.get_monthly_macro_data('20160101','20250831')

# 计算平滑值
credit_data['loan_yoy_smooth'] = credit_data['loan_yoy'].rolling(
    window=12, min_periods=1
).mean()

# 计算环比变化
credit_data['loan_yoy_change'] = credit_data['loan_yoy_smooth'].pct_change(3)

# 生成信号：上升为扩张(1)，下降为紧缩(0)
credit_data['loan_yoy_signal'] = np.where(
    credit_data['loan_yoy_change'] > 0, 1, -1
)
credit_signal = credit_data[['date', 'loan_yoy', 'loan_yoy_smooth', 'loan_yoy_change', 'loan_yoy_signal']].reset_index(drop=True)

# 获取社融数据
social_financing_data = db.get_monthly_macro_data('20160101','20250831')

# # 重命名列
# social_financing_data = social_financing_data.rename(columns={'social_financing_yoy': 'social_financing'})
# 同比
# social_financing_data['social_financing_yoy'] = social_financing_data['social_financing'].pct_change(12)

# 计算平滑值
social_financing_data['social_financing_smooth'] = social_financing_data['social_financing_yoy'].rolling(
    window=12, min_periods=1
).mean()

# 计算环比变化（相比3个月前）
social_financing_data['social_financing_change'] = social_financing_data['social_financing_smooth'].pct_change(3)

# 生成信号：上升为扩张(1)，下降为紧缩(0)
social_financing_data['social_financing_signal'] = np.where(
    social_financing_data['social_financing_change'] > 0, 1, -1
)
social_financing_signal = social_financing_data[['date', 'social_financing_yoy', 'social_financing_smooth', 'social_financing_change', 'social_financing_signal']].reset_index(drop=True)

# 信贷因子
stcok_credit_data = db.get_monthly_macro_data('20150101','20250831')
stcok_credit_data = stcok_credit_data[['date', 'social_financing', 'long_loan']].copy()

stcok_credit_data['social_financing_yoy'] = stcok_credit_data['social_financing'].pct_change(12)
stcok_credit_data['long_loan_sum'] = stcok_credit_data['long_loan'].rolling(window=12, min_periods=1).sum()

# 计算credit1: 12期指数加权移动平均的3期差值的符号
stcok_credit_data['credit1'] = stcok_credit_data['social_financing_yoy'].rolling(window=12, min_periods=1).mean().diff(3).apply(np.sign)

# 计算credit2: 12期百分比变化的3期差值的符号
stcok_credit_data['credit2'] = stcok_credit_data['long_loan_sum'].pct_change(12).diff(3).apply(np.sign)

# 计算credit3: 12期百分比变化的1期差值的符号
stcok_credit_data['credit3'] = stcok_credit_data['long_loan_sum'].pct_change(12).diff(1).apply(np.sign)

# 计算credit: 三列之和的符号
stcok_credit_data['credit'] = (stcok_credit_data[['credit1', 'credit2', 'credit3']].sum(axis=1)).apply(np.sign)


pmi_data = db.get_monthly_macro_data('20150101','20250831')
pmi_data = pmi_data[['date', 'pmi']].copy()

# 同比
pmi_data['pmi_yoy'] = pmi_data['pmi'].pct_change(12)
# 计算12个月移动平均
pmi_data['pmi_yoy_ma12'] = pmi_data['pmi_yoy'].rolling(window=12, min_periods=1).mean()

# 计算同比增长率（月度回看）
pmi_data['pmi_ma12_change'] = pmi_data['pmi_yoy_ma12'].pct_change(3)

# 生产因子得分
pmi_data['pmi_score'] = np.where(
    pmi_data['pmi_ma12_change'] > 0, 1, -1
)

inflation_data = db.get_monthly_macro_data('20150101','20250831')
inflation_data = inflation_data[['date', 'cpi_yoy', 'ppi_yoy']].copy()

# 计算12个月移动平均
inflation_data['cpi_ma12'] = inflation_data['cpi_yoy'].rolling(window=12, min_periods=1).mean()
inflation_data['ppi_ma12'] = inflation_data['ppi_yoy'].rolling(window=12, min_periods=1).mean()

# 消费因子（使用配置的权重）
inflation_data['consumption_factor'] = (
    0.5 * inflation_data['cpi_ma12'] +
    0.5 * inflation_data['ppi_ma12']
)

# 计算月度回看变化
inflation_data['cpi_ma12_change'] = inflation_data['cpi_ma12'].pct_change(3)
inflation_data['ppi_ma12_change'] = inflation_data['ppi_ma12'].pct_change(3)
inflation_data['consumption_factor_change'] = inflation_data['consumption_factor'].pct_change(3)

# 消费因子得分
inflation_data['cpi_score'] = np.where(
    inflation_data['cpi_ma12_change'] < 0, 1, -1
)
inflation_data['ppi_score'] = np.where(
    inflation_data['ppi_ma12_change'] < 0, 1, -1
)
inflation_data['consumption_score'] = np.where(
    inflation_data['consumption_factor_change'] < 0, 1, -1
)

# 制造业固定资产投资完成额累计同比因子
investment_data = db.get_monthly_macro_data('20150101','20250831')
investment_data = investment_data[['date', 'ppe_manu_yoy']].copy()

# 计算累计同比
investment_data['ppe_manu_yoy'] = investment_data['ppe_manu_yoy'].rolling(window=12, min_periods=1).sum()
investment_data['ppe_manu_yoy_change'] = investment_data['ppe_manu_yoy'].pct_change(3)

# 计算因子得分
investment_data['fixed_asset_investment_factor'] = np.where(
    investment_data['ppe_manu_yoy_change'] > 0, 1, -1
)

signal_data = pd.merge(
    dr007_signal, shibor_signal, on='date', how='left'
).merge(
    credit_signal, on='date', how='left'
).merge(
    social_financing_signal, on='date', how='left'
).merge(
    stcok_credit_data, on='date', how='left'
).merge(
    pmi_data, on='date', how='left'
).merge(
    inflation_data, on='date', how='left'
).merge(
    investment_data, on='date', how='left'
)
signal_data['month'] = signal_data['date'].dt.to_period('M')
signal_data

asset = 'stock'
stock_signal_data = signal_data.copy()
# signal_data['signal'] = signal_data[['dr007_signal', 'shibor_signal', 'loan_yoy_signal', 'social_financing_signal']].mean(axis=1)
# signal_data['monetary_score'] = signal_data[['dr007_signal', 'shibor_signal']].sum(axis=1)
# signal_data['credit_score'] = signal_data[['loan_yoy_signal', 'social_financing_signal']].sum(axis=1)
stock_signal_data['growth_score'] = np.where(stock_signal_data[['pmi_score', 'fixed_asset_investment_factor', 'cpi_score']].sum(axis=1) > 0, 1, -1)
stock_signal_data['signal'] = ((stock_signal_data['pmi_score'] + stock_signal_data['credit']) >= 0).astype(int)
# signal_data['signal'] = signal_data['monetary_score']
# signal_data

stock_backtest_data = pd.merge(monthly_price_data, stock_signal_data, on='date', how='left')
stock_backtest_data['signal'] = stock_backtest_data['signal'].shift(1)
stock_backtest_data = stock_backtest_data[stock_backtest_data['date'] >= '20170101'].reset_index(drop=True)
stock_backtest_data['benchmark_return'] = stock_backtest_data[asset]
stock_backtest_data['strategy_return'] = stock_backtest_data['signal'] * stock_backtest_data[asset]
stock_backtest_data['benchmark_nav'] = (1 + stock_backtest_data['benchmark_return']).cumprod()
stock_backtest_data['strategy_nav'] = (1 + stock_backtest_data['strategy_return']).cumprod()
# 补充首行数据
stock_backtest_data.loc[len(stock_backtest_data), 'benchmark_nav'] = 1
stock_backtest_data.loc[len(stock_backtest_data)-1, 'strategy_nav'] = 1
stock_backtest_data.loc[len(stock_backtest_data)-1, 'benchmark_return'] = 0
stock_backtest_data.loc[len(stock_backtest_data)-1, 'strategy_return'] = 0
stock_backtest_data.loc[len(stock_backtest_data)-1, 'date'] = pd.to_datetime('20170101')
stock_backtest_data = stock_backtest_data.sort_values('date').reset_index(drop=True)
stock_performance = calculate_annual_performance(stock_backtest_data)
# stock_performance.to_csv('stock_performance.csv', index=False, encoding='gbk')
stock_performance

asset = 'bond'
bond_signal_data = signal_data.copy()
# signal_data['signal'] = signal_data[['dr007_signal', 'shibor_signal', 'loan_yoy_signal', 'social_financing_signal']].mean(axis=1)
bond_signal_data['monetary_score'] = bond_signal_data[['dr007_signal', 'shibor_signal']].mean(axis=1)
bond_signal_data['credit_score'] = bond_signal_data[['loan_yoy_signal', 'social_financing_signal']].mean(axis=1)
bond_signal_data['signal'] = ((bond_signal_data['monetary_score'] - bond_signal_data['social_financing_signal']) >= 0).astype(int)
# signal_data['signal'] = signal_data['monetary_score']
# signal_data

bond_backtest_data = pd.merge(monthly_price_data, bond_signal_data, on='date', how='left')
bond_backtest_data['signal'] = bond_backtest_data['signal'].shift(1)
bond_backtest_data = bond_backtest_data[bond_backtest_data['date'] >= '20170101'].reset_index(drop=True)
bond_backtest_data['benchmark_return'] = bond_backtest_data[asset]
bond_backtest_data['strategy_return'] = bond_backtest_data['signal'] * bond_backtest_data[asset]
bond_backtest_data['benchmark_nav'] = (1 + bond_backtest_data['benchmark_return']).cumprod()
bond_backtest_data['strategy_nav'] = (1 + bond_backtest_data['strategy_return']).cumprod()
# 补充首行数据
bond_backtest_data.loc[len(bond_backtest_data), 'benchmark_nav'] = 1
bond_backtest_data.loc[len(bond_backtest_data)-1, 'strategy_nav'] = 1
bond_backtest_data.loc[len(bond_backtest_data)-1, 'benchmark_return'] = 0
bond_backtest_data.loc[len(bond_backtest_data)-1, 'strategy_return'] = 0
bond_backtest_data.loc[len(bond_backtest_data)-1, 'date'] = pd.to_datetime('20170101')
bond_backtest_data = bond_backtest_data.sort_values('date').reset_index(drop=True)
performance = calculate_annual_performance(bond_backtest_data)
# performance.to_csv(asset + '_2_performance.csv', index=False, encoding='gbk')
performance

bond_backtest_data[['date', 'benchmark_return', 'strategy_return', 'signal', 'benchmark_nav', 'strategy_nav']].to_csv('bond_2_result.csv', index=False)

fig = plot_nav_comparison(bond_backtest_data)

macro = pd.read_csv('./macro/macro.csv')
macro['month'] = pd.to_datetime(macro['Date'].astype(str)).dt.to_period('M')
macro.loc[len(macro), 'month'] = pd.to_datetime('20250831').to_period('M')
columns = macro.columns.difference(['Date', 'month'])
macro[columns] = macro[columns].shift(1)
macro

asset = 'bond'
signal = 'credict'
backtest_data = pd.merge(monthly_price_data, macro, on='month', how='left')
backtest_data = backtest_data[backtest_data['date'] >= '20170101'].reset_index(drop=True)
backtest_data['benchmark_return'] = backtest_data[asset]
# 信号大于0，方向为正
backtest_data['direction'] = np.where(backtest_data[signal] < 0, 1, 0)
backtest_data['strategy_return'] = backtest_data['direction'] * backtest_data[asset]
backtest_data['benchmark_nav'] = (1 + backtest_data['benchmark_return']).cumprod()
backtest_data['strategy_nav'] = (1 + backtest_data['strategy_return']).cumprod()
# 补充首行数据
backtest_data.loc[len(backtest_data), 'benchmark_nav'] = 1
backtest_data.loc[len(backtest_data)-1, 'strategy_nav'] = 1
backtest_data.loc[len(backtest_data)-1, 'benchmark_return'] = 0
backtest_data.loc[len(backtest_data)-1, 'strategy_return'] = 0
backtest_data.loc[len(backtest_data)-1, 'date'] = pd.to_datetime('20170101')
backtest_data = backtest_data.sort_values('date').reset_index(drop=True)
calculate_annual_performance(backtest_data)

import math
def calculate_risk_parity_weights(price_data, ad_signal = True, stock_signal_data = None, bond_signal_data = None, window = 1500):
    risk_parity_weight = []
    # 选取每个月第一天的日期，用历史数据计算风险平价权重
    date_list = price_data.groupby('month').first()['date'].to_list()
    for date in date_list:
        i = price_data[price_data['date'] == date].index[0]
        if i < window:
            continue
        window_data = price_data.loc[i - window:i, ['stock', 'bond', 'convertingbonds', 'sp500ETF', 'aaadebt']]
        stock_ad = 0
        bond_ad = 0
        if ad_signal and stock_signal_data is not None:
            stock_ad = stock_signal_data[stock_signal_data['month'] == (date-timedelta(days=20)).to_period('M')]['signal'].values[0] - 0.5
        if ad_signal and bond_signal_data is not None:
            bond_ad = bond_signal_data[bond_signal_data['month'] == (date-timedelta(days=20)).to_period('M')]['signal'].values[0] - 0.5
        
        # 确定风险预算
        budget = np.ones(5)
        if ad_signal:
            budget = budget + np.array([0.8,0,0,0,-0.8]) * stock_ad + np.array([0,0.8,0,0,-0.8]) * bond_ad
            budget = budget / min(budget)
            k = math.log(30) / math.log(max(budget))
            budget = budget ** k
        budget = budget / sum(budget)

        aweights = []
        for j in range(1, len(window_data) + 1):
            aweights.append(0.99 ** (len(window_data) - j))

        # 风险平价框架优化
        ExpCovariance = np.cov(window_data.fillna(0), aweights=aweights, rowvar=False)
        w_lb = [0, 0, 0, 0, 0]
        w_ub = [0.2, 1, 0.1, 0.05, 0.4]
        CRB = ConstrainedRiskBudgeting(ExpCovariance, budget, bounds=np.array([w_lb, w_ub]).T)
        CRB.solve()
        w = CRB.x
        if ad_signal:
            suffix = '_weight_ad'
        else:
            suffix = '_weight'
        signal_weight = {
            'date': date, 
            'stock'+suffix: w[0], 
            'bond'+suffix: w[1],
            'convertingbonds'+suffix: w[2],
            'sp500ETF'+suffix: w[3],
            'aaadebt'+suffix: w[4]
            }
        risk_parity_weight.append(signal_weight)
    risk_parity_weight = pd.DataFrame(risk_parity_weight)
    return risk_parity_weight
# 计算风险平价权重
risk_parity_weight = calculate_risk_parity_weights(price_data, False)
risk_parity_weight['month'] = risk_parity_weight['date'].dt.to_period('M')
# 加入择时信号调整后
risk_parity_weight_ad = calculate_risk_parity_weights(price_data, True, stock_signal_data, bond_signal_data)
risk_parity_weight_ad['month'] = risk_parity_weight_ad['date'].dt.to_period('M')

risk_parity_weight_ad.describe()

# 使用月度收益率进行回测
profolio_backtest_data = pd.merge(
    monthly_price_data, 
    stock_signal_data[['date', 'signal']].rename(columns={'signal': 'stock_signal'}), 
    on='date', 
    how='left'
)
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    bond_signal_data[['date', 'signal']].rename(columns={'signal': 'bond_signal'}), 
    on='date', 
    how='left'
)
asset_list = ['stock', 'bond', 'convertingbonds', 'sp500ETF', 'aaadebt']
benchmark_weight = ['stock_weight', 'bond_weight', 'convertingbonds_weight', 'sp500ETF_weight', 'aaadebt_weight']
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    risk_parity_weight[['month'] + benchmark_weight], 
    on='month', 
    how='left'
)
strategy_weight = ['stock_weight_ad', 'bond_weight_ad', 'convertingbonds_weight_ad', 'sp500ETF_weight_ad', 'aaadebt_weight_ad']
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    risk_parity_weight_ad[['month'] + strategy_weight], 
    on='month', 
    how='left'
)
profolio_backtest_data['stock_signal'] = profolio_backtest_data['stock_signal'].shift(1)
profolio_backtest_data['bond_signal'] = profolio_backtest_data['bond_signal'].shift(1)
profolio_backtest_data = profolio_backtest_data[profolio_backtest_data['date'] >= '20170101'].reset_index(drop=True)
# profolio_backtest_data['benchmark_return'] = profolio_backtest_data['bond']
profolio_backtest_data['benchmark_return'] = np.sum(np.array(profolio_backtest_data[benchmark_weight]) * 
                                                    np.array(profolio_backtest_data[asset_list]), axis=1)
profolio_backtest_data['benchmark_nav'] = (1 + profolio_backtest_data['benchmark_return']).cumprod()
# profolio_backtest_data['stock_weight'] = stock_weight + (profolio_backtest_data['stock_signal'] - 0.5) * 0.1
# profolio_backtest_data['bond_weight'] = bond_weight + (profolio_backtest_data['bond_signal'] - 0.5) * 0.1
profolio_backtest_data['strategy_return'] = np.sum(np.array(profolio_backtest_data[strategy_weight]) *
                                                np.array(profolio_backtest_data[asset_list]), axis=1)
profolio_backtest_data['strategy_nav'] = (1 + profolio_backtest_data['strategy_return']).cumprod()
# 补充首行数据
profolio_backtest_data.loc[len(profolio_backtest_data), 'benchmark_nav'] = 1
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'strategy_nav'] = 1
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'benchmark_return'] = 0
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'strategy_return'] = 0
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'date'] = pd.to_datetime('20170101')
profolio_backtest_data = profolio_backtest_data.sort_values('date').reset_index(drop=True)
profolio_performance = calculate_profolio_annual_performance(profolio_backtest_data)
# profolio_performance.to_csv('profolio_performance_monthly.csv', index=False, encoding='gbk')
profolio_performance

profolio_backtest_data

# 使用日度收益率进行回测
stock_signal_data_lag = stock_signal_data[['month', 'signal']].copy()
stock_signal_data_lag['signal'] = stock_signal_data_lag['signal'].shift(1)
bond_signal_data_lag = bond_signal_data[['month', 'signal']].copy()
bond_signal_data_lag['signal'] = bond_signal_data_lag['signal'].shift(1)
profolio_backtest_data = pd.merge(
    price_data, 
    stock_signal_data_lag[['month', 'signal']].rename(columns={'signal': 'stock_signal'}), 
    on='month', 
    how='left'
)
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    bond_signal_data_lag[['month', 'signal']].rename(columns={'signal': 'bond_signal'}), 
    on='month', 
    how='left'
)
asset_list = ['stock', 'bond', 'convertingbonds', 'sp500ETF', 'aaadebt']
benchmark_weight = ['stock_weight', 'bond_weight', 'convertingbonds_weight', 'sp500ETF_weight', 'aaadebt_weight']
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    risk_parity_weight[['month'] + benchmark_weight], 
    on='month', 
    how='left'
)
strategy_weight = ['stock_weight_ad', 'bond_weight_ad', 'convertingbonds_weight_ad', 'sp500ETF_weight_ad', 'aaadebt_weight_ad']
profolio_backtest_data = pd.merge(
    profolio_backtest_data, 
    risk_parity_weight_ad[['month'] + strategy_weight], 
    on='month', 
    how='left'
)
profolio_backtest_data = profolio_backtest_data[profolio_backtest_data['date'] >= '20170101'].reset_index(drop=True)
profolio_backtest_data['benchmark_return'] = np.sum(np.array(profolio_backtest_data[benchmark_weight]) * 
                                                    np.array(profolio_backtest_data[asset_list]/100), axis=1)
profolio_backtest_data['benchmark_nav'] = (1 + profolio_backtest_data['benchmark_return']).cumprod()
# profolio_backtest_data['stock_weight'] = stock_weight + (profolio_backtest_data['stock_signal'] - 0.5) * 0.1
# profolio_backtest_data['bond_weight'] = bond_weight + (profolio_backtest_data['bond_signal'] - 0.5) * 0.1
profolio_backtest_data['strategy_return'] = np.sum(np.array(profolio_backtest_data[strategy_weight]) *
                                                np.array(profolio_backtest_data[asset_list]/100), axis=1)
profolio_backtest_data['strategy_nav'] = (1 + profolio_backtest_data['strategy_return']).cumprod()
# 补充首行数据
profolio_backtest_data.loc[len(profolio_backtest_data), 'benchmark_nav'] = 1
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'strategy_nav'] = 1
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'benchmark_return'] = 0
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'strategy_return'] = 0
profolio_backtest_data.loc[len(profolio_backtest_data)-1, 'date'] = pd.to_datetime('20170101')
profolio_backtest_data = profolio_backtest_data.sort_values('date').reset_index(drop=True)
profolio_performance = calculate_profolio_annual_performance(profolio_backtest_data, 'D')
# profolio_performance.to_csv('profolio_performance_daily.csv', index=False, encoding='gbk')
profolio_performance

profolio_backtest_data.to_csv('profolio_backtest_data_mongthly.csv', index=False, encoding='gbk')

fig = plot_nav_comparison(profolio_backtest_data)